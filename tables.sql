CREATE TABLE public.daily_stats (
    vehicle_id int4 NOT NULL,
    "date" date NOT NULL,
    status_code int4 NULL DEFAULT 0,
    timestamp_start int4 NOT NULL,
    timestamp_end int4 NULL,
    km_start float4 NULL,
    km_end float4 NULL,
    ignition_count int4 NULL,
    ignition_time interval NULL,
    stops int4 NULL,
    speed_max float4 NULL,
    speed_avg float4 NULL,
    driving_time interval NULL,
    gps_distance float4 NULL,
    ascent float4 NULL,
    descent float4 NULL,
    drivemode_d_time interval NULL,
    drivemode_n_time interval NULL,
    drivemode_r_time interval NULL,
    drivemode_e_time interval NULL,
    t_ambient_start float4 NULL,
    t_ambient_end float4 NULL,
    t_ambient_min float4 NULL,
    t_ambient_max float4 NULL,
    t_ambient_avg float4 NULL,
    energy_soc float4 NULL,
    energy_per100km_soc float4 NULL,
    recuperated_energy float4 NULL,
    temp_min float4 NULL,
    temp_max float4 NULL,
    u_min float4 NULL,
    u_max float4 NULL,
    i_min float4 NULL,
    i_max float4 NULL,
    recuperations int4 NULL,
    accelerations int4 NULL,
    hand_brake_count int4 NULL,
    door_open_count int4 NULL,
    belt_use_count int4 NULL,
    door_lock_count int4 NULL,
    front_window_heating_count int4 NULL,
    front_window_heating_time interval NULL,
    left_indicator_time interval NULL,
    right_indicator_time interval NULL,
    hazzard_time interval NULL,
    low_beam_time interval NULL,
    low_beam_count int4 NULL,
    high_beam_time interval NULL,
    high_beam_count int4 NULL,
    parking_light_time interval NULL,
    parking_light_count int4 NULL,
    rear_fog_light_time interval NULL,
    rear_fog_light_count int4 NULL,
    lat_start float4 NULL,
    lon_start float4 NULL,
    energy_integrated float8 NULL,
    energy_per100km_integrated float8 NULL,
    hand_brake_time interval NULL,
    lat_end float4 NULL,
    lon_end float4 NULL,
    soc_start_percent float4 NULL,
    soc_end_percent float4 NULL,
    soc_start_kwh float4 NULL,
    soc_end_kwh float4 NULL,
    heating_energy float4 NULL,
    climate_energy float4 NULL,
    left_indicator_count int4 NULL,
    right_indicator_count int4 NULL,
    hazzard_count int4 NULL,
    delivery_stops int4 NULL,
    drivemode_p_time interval NULL,
    CONSTRAINT daily_stats_unique UNIQUE (vehicle_id, date, timestamp_start),
    CONSTRAINT daily_stats_staging_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(vehicle_id) ON DELETE CASCADE
);

CREATE TABLE public.vehicles (
    vehicle_id serial4 NOT NULL,
    depot_id int4 NOT NULL,
    station_id int4 NULL,
    usable_battery_capacity float8 NULL,
    c2cbox text NULL,
    vin text NOT NULL,
    "name" text NULL,
    code text NULL,
    lat float8 NULL,
    lon float8 NULL,
    precondition_duration int4 DEFAULT 1800 NOT NULL,
    emergency_charge_time timestamptz NULL,
    update_timestamp int4 NULL,
    ikz text NULL,
    charger_controllable bool DEFAULT true NOT NULL,
    vin_bcm text NULL,
    fallback_power_odd float8 DEFAULT 1500 NOT NULL,
    fallback_power_even float8 DEFAULT 1500 NOT NULL,
    refitted_c2c bool DEFAULT false NOT NULL,
    three_phase_charger bool DEFAULT false NOT NULL,
    charger_power int4 DEFAULT 3000 NOT NULL,
    late_charging bool DEFAULT false NOT NULL,
    late_charging_time time NULL,
    finished_status bool DEFAULT false NULL,
    enable_preconditioning bool DEFAULT true NOT NULL,
    gps_timestamp timestamptz NULL,
    qmlocked bool DEFAULT false NOT NULL,
    special_qs_approval bool DEFAULT false NOT NULL,
    precondition_power int4 DEFAULT 1500 NULL,
    vehicle_variant int4 NULL,
    color_id int4 DEFAULT 1 NOT NULL,
    options_group_info int4 NULL,
    external_id int4 NULL,
    penta_kennwort text NULL,
    penta_number_id int4 NULL,
    max_soc int4 NULL,
    park_id int4 NULL,
    park_position int4 NULL,
    mlat float8 NULL,
    mlon float8 NULL,
    mgps_user text NULL,
    mgps_timestamp timestamptz NULL,
    depot_dispatch_date timestamptz NULL,
    special_qs_approval_set_user int4 NULL,
    special_qs_approval_set_time timestamptz NULL,
    special_qs_approval_comment text NULL,
    mcomment text NULL,
    replacement_vehicles bool DEFAULT false NULL,
    sub_vehicle_configuration_id int4 NULL,
    penta_variant_id int4 NULL,
    penta_keyword text NULL,
    version_coc int4 DEFAULT 1 NOT NULL,
    organisational_unit_id int4 NULL,
    vehicle_attribute_set_id int4 NULL,
    lulm_active bool DEFAULT false NOT NULL,
    CONSTRAINT vehicles_pkey PRIMARY KEY (vehicle_id),
    CONSTRAINT vehicles_usable_battery_capacity_check CHECK (
        (
            usable_battery_capacity >= (10240) :: double precision
        )
    ),
    CONSTRAINT vehicles_vin_key UNIQUE (vin),
    CONSTRAINT fkey_depot_id FOREIGN KEY (depot_id) REFERENCES public.depots(depot_id),
    CONSTRAINT fkey_station_id FOREIGN KEY (station_id) REFERENCES public.stations(station_id),
    CONSTRAINT vehicles_color_id_fkey FOREIGN KEY (color_id) REFERENCES public.colors(color_id),
    CONSTRAINT vehicles_organisational_unit_id_fkey FOREIGN KEY (organisational_unit_id) REFERENCES public.organisational_units(organisational_unit_id) ON DELETE
    SET
        NULL,
        CONSTRAINT vehicles_park_id_fkey FOREIGN KEY (park_id) REFERENCES public.park_lines(park_id),
        CONSTRAINT vehicles_penta_number_id_fkey FOREIGN KEY (penta_number_id) REFERENCES public.penta_numbers(penta_number_id),
        CONSTRAINT vehicles_penta_variant_id_fkey FOREIGN KEY (penta_variant_id) REFERENCES public.penta_variants(penta_variant_id),
        CONSTRAINT vehicles_special_qs_approval_set_user_fkey FOREIGN KEY (special_qs_approval_set_user) REFERENCES public.users(id),
        CONSTRAINT vehicles_sub_vehicle_configuration_id_fkey FOREIGN KEY (sub_vehicle_configuration_id) REFERENCES public.sub_vehicle_configurations(sub_vehicle_configuration_id),
        CONSTRAINT vehicles_vehicle_attribute_set_id_fkey FOREIGN KEY (vehicle_attribute_set_id) REFERENCES public.vehicle_attribute_sets(vehicle_attribute_set_id) ON DELETE
    SET
        NULL,
        CONSTRAINT vehicles_vehicle_variant_fkey FOREIGN KEY (vehicle_variant) REFERENCES public.vehicle_variants(vehicle_variant_id)
);