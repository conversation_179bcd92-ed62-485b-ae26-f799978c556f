#!/usr/bin/env python3
"""
Script to compare battery ages between <PERSON><PERSON><PERSON>'s file and generated file.
Compares battery_age values for matching battery_id entries.
"""

import pandas as pd
import sys
from pathlib import Path

def compare_battery_ages():
    """Compare battery ages between Accure file and generated file."""
    
    # File paths
    accure_file = Path("output/Batterie_29-07-25.csv")
    generated_file = Path("output/accure.csv")
    
    # Check if files exist
    if not accure_file.exists():
        print(f"Error: {accure_file} not found!")
        return False
    
    if not generated_file.exists():
        print(f"Error: {generated_file} not found!")
        return False
    
    try:
        # Read both CSV files
        print("Reading Accure file...")
        accure_df = pd.read_csv(accure_file)
        
        print("Reading generated file...")
        generated_df = pd.read_csv(generated_file)
        
        # Check if required columns exist
        required_cols = ['battery_id', 'battery_age']
        for col in required_cols:
            if col not in accure_df.columns:
                print(f"Error: Column '{col}' not found in Accure file!")
                return False
            if col not in generated_df.columns:
                print(f"Error: Column '{col}' not found in generated file!")
                return False
        
        print(f"\nAccure file: {len(accure_df)} batteries")
        print(f"Generated file: {len(generated_df)} batteries")
        
        # Create dictionaries for quick lookup
        accure_ages = dict(zip(accure_df['battery_id'], accure_df['battery_age']))
        generated_ages = dict(zip(generated_df['battery_id'], generated_df['battery_age']))
        
        # Find common battery IDs
        accure_ids = set(accure_ages.keys())
        generated_ids = set(generated_ages.keys())
        common_ids = accure_ids.intersection(generated_ids)
        
        print(f"\nCommon battery IDs: {len(common_ids)}")
        print(f"Only in Accure file: {len(accure_ids - generated_ids)}")
        print(f"Only in generated file: {len(generated_ids - accure_ids)}")
        
        # Compare battery ages for common IDs
        matches = 0
        mismatches = 0
        mismatch_details = []
        
        for battery_id in common_ids:
            accure_age = accure_ages[battery_id] + 1
            generated_age = generated_ages[battery_id]
            
            if accure_age == generated_age:
                matches += 1
            else:
                mismatches += 1
                mismatch_details.append({
                    'battery_id': battery_id,
                    'accure_age': accure_age,
                    'generated_age': generated_age,
                    'difference': generated_age - accure_age
                })
        
        # Print summary
        print(f"\n=== COMPARISON RESULTS ===")
        print(f"Total comparisons: {len(common_ids)}")
        print(f"Matches: {matches} ({matches/len(common_ids)*100:.1f}%)")
        print(f"Mismatches: {mismatches} ({mismatches/len(common_ids)*100:.1f}%)")
        
        # Show mismatch details
        if mismatches > 0:
            print(f"\n=== MISMATCH DETAILS ===")
            print("Battery ID | Accure Age | Generated Age | Difference")
            print("-" * 55)
            
            # Sort by absolute difference (largest first)
            mismatch_details.sort(key=lambda x: abs(x['difference']), reverse=True)
            
            for detail in mismatch_details[:20]:  # Show first 20 mismatches
                print(f"{detail['battery_id']:>10} | {detail['accure_age']:>10} | {detail['generated_age']:>13} | {detail['difference']:>10}")
            
            if len(mismatch_details) > 20:
                print(f"... and {len(mismatch_details) - 20} more mismatches")
            
            # Statistics on differences
            differences = [abs(d['difference']) for d in mismatch_details]
            print(f"\nDifference statistics:")
            print(f"  Average absolute difference: {sum(differences)/len(differences):.1f} days")
            print(f"  Maximum absolute difference: {max(differences)} days")
            print(f"  Minimum absolute difference: {min(differences)} days")
        
        # Save detailed comparison to file
        if common_ids:
            comparison_data = []
            for battery_id in sorted(common_ids):
                accure_age = accure_ages[battery_id]
                generated_age = generated_ages[battery_id]
                comparison_data.append({
                    'battery_id': battery_id,
                    'accure_age': accure_age,
                    'generated_age': generated_age,
                    'difference': generated_age - accure_age,
                    'match': accure_age == generated_age
                })
            
            comparison_df = pd.DataFrame(comparison_data)
            output_file = "output/battery_age_comparison.csv"
            comparison_df.to_csv(output_file, index=False)
            print(f"\nDetailed comparison saved to: {output_file}")
        
        return mismatches == 0
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        return False

if __name__ == "__main__":
    success = compare_battery_ages()
    sys.exit(0 if success else 1)
