#!/usr/bin/env python3
"""
Generate Working CSV Files for Vehicle-Battery Comparison

This script generates three specific CSV files:
1. working_matching_vehicles.csv - vehicles that match between both files with columns: akz, vin, master, slave, erstzulassung, note
2. working_unique_vehicles.csv - vehicles unique to each file with source information
3. working_mismatches.csv - vehicles with battery or date mismatches between the files

Additionally, it validates that all vehicles are covered across the three files.

Usage:
    python generate_working_csvs.py
"""

import logging
from pathlib import Path
from others.vehicle_battery_comparison import VehicleBatteryComparator

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Generate working CSV files only."""

    # File paths
    file1_path = "Fahrzeug Batterie Zuordnung-with-batteryid.xlsx"
    file2_path = "Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx"

    # Check if files exist
    if not Path(file1_path).exists():
        logger.error(f"File not found: {file1_path}")
        return

    if not Path(file2_path).exists():
        logger.error(f"File not found: {file2_path}")
        return

    logger.info("=" * 60)
    logger.info("GENERATING WORKING CSV FILES")
    logger.info("=" * 60)

    # Create comparator and generate working CSV files
    comparator = VehicleBatteryComparator(file1_path, file2_path)

    try:
        exported_files = comparator.generate_working_csvs_only()

        logger.info("=" * 60)
        logger.info("GENERATION COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)

        logger.info("Generated files:")
        for file_type, file_path in exported_files.items():
            logger.info(f"  {file_type}: {file_path}")

        logger.info("\nFile descriptions:")
        logger.info("  working_matching_vehicles.csv: Vehicles present in both files")
        logger.info("    - Columns: akz, vin, master, slave, erstzulassung, note")
        logger.info("    - Note indicates if date match required month/day swapping")
        logger.info("  working_unique_vehicles.csv: Vehicles unique to each file")
        logger.info("    - Columns: akz, vin, master, slave, erstzulassung, note")
        logger.info("    - Note indicates which source file the vehicle comes from")
        logger.info("  working_mismatches.csv: Vehicles with battery/date mismatches")
        logger.info("    - Columns: akz, vin, master, slave, erstzulassung, note")
        logger.info("    - Note describes the specific mismatch found")
        logger.info(
            "  coverage_validation.txt: Validation report ensuring complete coverage"
        )

    except Exception as e:
        logger.error(f"Failed to generate working CSV files: {e}")
        raise


if __name__ == "__main__":
    main()
