import logging
import argparse

from pathlib import Path

from battery_timeline.logging_config import setup_logging
from battery_timeline.report import BatteryReportGenerator
from battery_timeline.orchestrator import BatteryTimelineGenerator
from battery_timeline.accure_validator import AccureValidator

logger = logging.getLogger(__name__)


def run_generator() -> None:
    csv_file, stats_file = BatteryTimelineGenerator().run()
    print(f"✅  generator done → {csv_file}\n📊  stats → {stats_file}")


def run_reporter(timeline: Path) -> None:
    reporter = BatteryReportGenerator(timeline_path=timeline)
    age_csv, detailed_csv, stats = reporter.run()
    print(f"✅  reporter done:")
    if age_csv:
        print(f"   📄  battery age report → {age_csv}")
    if detailed_csv:
        print(f"   📄  detailed lifecycle report → {detailed_csv}")
    if stats:
        print(f"   📊  stats → {stats}")


def run_accure_validator() -> None:
    validator = AccureValidator()
    results = validator.validate()

    # Save results to JSON file
    json_path = validator.save_validation_results()

    print(f"✅  accure validator done:")
    print(f"   📊  results → {results}")
    print(f"   💾  JSON saved → {json_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser("battery-timeline")
    parser.add_argument(
        "--report",
        action="store_true",
        help="Run BatteryReportGenerator instead of the generator",
    )
    parser.add_argument(
        "-d", "--debug", action="store_true", help="Enable DEBUG level logging"
    )
    parser.add_argument(
        "-v", "--validate_accure", action="store_true", help="Run AccureValidator"
    )

    args = parser.parse_args()

    if args.report:
        setup_logging(
            debug=args.debug, log_file=Path("output/report_battery_timeline.log")
        )
        run_reporter(Path("output/battery_lifecycle_timelines.csv"))
    elif args.validate_accure:
        setup_logging(debug=args.debug, log_file=Path("output/validate_accure.log"))
        run_accure_validator()
    else:
        setup_logging(debug=args.debug, log_file=Path("output/battery_timeline.log"))
        run_generator()
