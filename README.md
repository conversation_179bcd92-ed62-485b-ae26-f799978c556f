# Battery Timeline Analysis Tool

A Python application for analyzing electric vehicle battery lifecycles and generating comprehensive reports on battery usage patterns, lifecycle stages, and operational statistics for StreetScooter vehicles.

## Overview

This tool processes battery repair events, vehicle data, and daily operational statistics to generate detailed battery timeline reports. It provides insights into battery lifecycles, vehicle assignments, dual-battery configurations, and operational metrics like odometer readings (km_stand).

## Features

- **Battery Timeline Generation**: Creates comprehensive lifecycle timelines for all batteries
- **Battery Age Analysis**: Calculates battery age and generates summary reports
- **Detailed Lifecycle Reports**: Per-interval analysis with odometer data
- **Dual-Battery Detection**: Identifies vehicles with concurrent battery configurations
- **Data Validation**: Validates timeline integrity and resolves conflicts
- **Multi-threaded Processing**: Optimized for large datasets
- **PostgreSQL Integration**: Direct database connectivity for real-time data

## Project Structure

```
battery-km-stand/
├── main.py                          # Main entry point
├── requirements.txt                 # Python dependencies
├── tables.sql                       # Database schema
├── battery_timeline/                # Core analysis modules
│   ├── analyzer.py                  # Battery analysis and reporting
│   ├── orchestrator.py              # Main processing pipeline
│   ├── preparator.py                # Data loading and preparation
│   ├── processing.py                # Battery event processing
│   ├── validator.py                 # Timeline validation
│   ├── output_generator.py          # Report generation
│   ├── models.py                    # Data models and types
│   └── logging_config.py            # Logging configuration
├── input/                           # Input data files
│   ├── hv_repair_2025-06-02b.csv   # Battery repair events
│   ├── daily_stats.csv             # Vehicle operational data
│   ├── battery_type.csv             # Battery type mappings
│   ├── working_matching_vehicles.csv
│   └── working_unique_vehicles.csv
├── output/                          # Generated reports
│   ├── battery_lifecycle_timelines.csv      # Complete timeline data
│   ├── accure.csv                           # Battery age summary
│   ├── battery_lifecycle_detailed.csv      # Detailed interval report
│   └── battery_timeline_statistics.json    # Processing statistics
└── others/                          # Utility scripts
    ├── export_daily_stats.py       # Database export utility
    ├── generate_battery_type.py    # Battery type generation
    ├── generate_working_csvs.py    # Working vehicle data generation
    └── vehicle_battery_comparison.py
```

## Installation

### Prerequisites

- Python 3.8+
- PostgreSQL database access
- Required Python packages (see requirements.txt)

### Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd battery-km-stand
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure database connection**:
   Set environment variables for PostgreSQL connection:
   ```bash
   export DB_HOST=localhost
   export DB_PORT=6543
   export DB_NAME=LeitwartenDB
   export DB_USER=datadump
   export DB_PASSWORD=your_password
   ```

5. **Prepare input data**:
   Ensure required CSV files are in the `input/` directory:
   - `hv_repair_2025-06-02b.csv`
   - `daily_stats.csv`
   - `battery_type.csv`
   - `working_matching_vehicles.csv`
   - `working_unique_vehicles.csv`

## Usage

### Basic Timeline Generation

Generate battery lifecycle timelines:

```bash
python main.py
```

This will:
- Process all battery repair events
- Generate timeline intervals for each battery
- Validate timeline integrity
- Output results to `output/battery_lifecycle_timelines.csv`
- Create processing statistics in `output/battery_timeline_statistics.json`

### Battery Age Analysis

Run detailed battery analysis on generated timelines:

```bash
python main.py --analyze
```

This will:
- Load existing timeline data
- Calculate battery ages and lifecycle stages
- Generate battery age summary (`output/accure.csv`)
- Create detailed interval report (`output/battery_lifecycle_detailed.csv`)
- Calculate odometer differences (km_stand) for each interval

### Debug Mode

Enable detailed logging:

```bash
python main.py --debug
python main.py --analyze --debug
```
