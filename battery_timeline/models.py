# battery_timeline/models.py
from datetime import date
from typing import List, Optional, TypedDict

class BatteryEvent(TypedDict):
    vin: str
    date: date
    column: Optional[str]        # "old", "new", or None
    event_id: Optional[int]
    row_data: Optional[object]

class BatteryInterval(TypedDict):
    battery_id: str
    vin: str
    interval_start: Optional[date]
    interval_end: Optional[date]
    interval_type: str
    lifecycle_stage: str
    source_event_ids: List[Optional[int]]
    confidence: float
    notes: str
