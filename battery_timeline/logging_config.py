# battery_timeline/logging_config.py
import logging, logging.config, sys
from pathlib import Path
from typing import Union


def setup_logging(*, debug: bool = False, log_file: Union[str, Path]) -> None:
    """
    Minimal dictConfig wrapper: console + file, same format as basicConfig.
    Call this once in your CLI or __main__.
    """
    level = "DEBUG" if debug else "INFO"

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "std": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": "std",
                    "stream": "ext://sys.stdout",
                    "level": level,
                },
                "file": {
                    "class": "logging.FileHandler",
                    "formatter": "std",
                    "filename": str(log_file),
                    "level": level,
                },
            },
            "root": {
                "handlers": ["console", "file"],
                "level": level,
            },
        }
    )
