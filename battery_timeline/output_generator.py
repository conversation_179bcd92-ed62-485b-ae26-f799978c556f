from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Mapping, Sequence, Protocol, Tuple

import pandas as pd
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class OutputBundle:
    """Plain data ↔ OutputGenerator boundary."""

    records: Sequence[
        Mapping[str, Any]
    ]  # e.g. list[dict] or DataFrame.to_dict("records")
    stats: Mapping[str, Any]  # lightweight, JSON‑serialisable
    csv_path: Path
    stats_path: Path


class WriterProtocol(Protocol):
    """Minimal surface area every writer must expose."""

    def write(
        self,
        df: pd.DataFrame,
        stats: Mapping[str, Any],
        csv_path: Path,
        stats_path: Path,
    ) -> Tuple[Path, Path]:
        """Return the final locations of the two artefacts."""
        ...


class LocalCSVWriter:
    """Local filesystem CSV writer implementation."""

    def write(
        self,
        df: pd.DataFrame,
        stats: Mapping[str, Any],
        csv_path: Path,
        stats_path: Path,
    ) -> Tuple[Path, Path]:
        """Write DataFrame and stats to local filesystem."""
        # Ensure directories exist
        csv_path.parent.mkdir(parents=True, exist_ok=True)
        stats_path.parent.mkdir(parents=True, exist_ok=True)

        # Write CSV
        df.to_csv(csv_path, index=False)
        logger.info(f"Saved data to {csv_path}")

        # Write stats as JSON
        with open(stats_path, "w", encoding="utf-8") as fh:
            json.dump(stats, fh, indent=2, default=str)
        logger.info(f"Saved statistics to {stats_path}")

        return csv_path, stats_path


class OutputGenerator:
    """Delegates all I/O to an injected writer implementation."""

    def __init__(self, writer: WriterProtocol):
        self.writer = writer

    def __call__(self, bundle: OutputBundle) -> tuple[Path, Path]:
        """Generate output files from bundle data."""
        df = self._to_dataframe(bundle.records)
        csv_file, stats_file = self.writer.write(
            df, bundle.stats, bundle.csv_path, bundle.stats_path
        )
        logger.info("✅ Output written: %s, %s", csv_file, stats_file)
        return csv_file, stats_file

    # ───────── pure helper ─────────
    @staticmethod
    def _to_dataframe(records):
        """Convert records to DataFrame with basic sorting."""
        df = pd.DataFrame(records)
        if "battery_id" in df.columns:
            return df.sort_values("battery_id")
        return df
